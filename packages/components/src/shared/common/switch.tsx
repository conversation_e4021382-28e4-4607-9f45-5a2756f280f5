import clsx from 'clsx'
import type { FC } from 'react'
import { forwardRef, useId } from 'react'
import { Text } from './text'

interface SwitchStylesProps {
  variant?: 'ios' | 'bordered'
  size?: 'sm' | 'md' | 'lg'
  className?: string | undefined
  error?: boolean | undefined
  disabled?: boolean | undefined
}

const useSwitchStyles = ({ className, variant = 'ios', size = 'md', error, disabled }: SwitchStylesProps) => {
  const baseStyles = clsx(
    'relative inline-flex items-center',
    'cursor-pointer transition-all duration-300 ease-in-out',
    'focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary',
    {
      'cursor-not-allowed opacity-50': disabled,
    },
    className,
  )

  const trackStyles = clsx('relative inline-flex items-center transition-all duration-300 ease-in-out', {
    // iOS variant styles
    'rounded-full': variant === 'ios',
    'bg-accessory-1': variant === 'ios' && !error,
    'bg-error/30': variant === 'ios' && error,

    // Bordered variant styles
    'rounded-b1 border': variant === 'bordered',
    'bg-white border-accessory-1': variant === 'bordered' && !error,
    'bg-error-lighter border-error': variant === 'bordered' && error,

    // Size variants
    'h-8 w-14': size === 'sm',
    'h-10 w-20': size === 'md',
    'h-12 w-24': size === 'lg',
  })

  const thumbStyles = clsx('absolute transition-all duration-300 ease-in-out transform', 'bg-white shadow-sm', {
    // iOS variant styles
    'rounded-full': variant === 'ios',

    // Bordered variant styles
    'rounded-[4px]': variant === 'bordered',

    // Size variants
    'h-6 w-6 left-1': size === 'sm',
    'h-8 w-8 left-1': size === 'md',
    'h-10 w-10 left-1': size === 'lg',
  })

  return { baseStyles, trackStyles, thumbStyles }
}

interface SwitchProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'disabled' | 'size'>,
    SwitchStylesProps {
  label?: string
  onCheckedChange?: (checked: boolean) => void
}

export const Switch: FC<SwitchProps> = forwardRef<HTMLInputElement, SwitchProps>(
  (
    {
      className,
      variant = 'ios',
      size = 'md',
      error = false,
      label,
      checked,
      disabled,
      onCheckedChange,
      onChange,
      ...props
    },
    ref,
  ) => {
    const { baseStyles, trackStyles, thumbStyles } = useSwitchStyles({ variant, size, className, error, disabled })
    const id = useId()

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      if (!disabled) {
        onCheckedChange?.(event.target.checked)
        onChange?.(event)
      }
    }

    const getThumbTransform = () => {
      if (!checked) {
        return 'translateX(0)'
      }

      switch (size) {
        case 'sm':
          return 'translateX(1.5rem)' // 24px
        case 'md':
          return 'translateX(2.5rem)' // 40px
        case 'lg':
          return 'translateX(4rem)' // 64px
        default:
          return 'translateX(2.5rem)'
      }
    }

    const getTrackColor = () => {
      if (disabled) {
        return variant === 'ios' ? 'bg-accessory-1' : 'bg-accessory-2'
      }
      if (error) {
        return variant === 'ios' ? 'bg-error/30' : 'bg-error-lighter'
      }
      if (checked) {
        return variant === 'ios' ? 'bg-primary' : 'bg-primary-light'
      }
      return variant === 'ios' ? 'bg-accessory-1' : 'bg-white'
    }

    return (
      <div className='flex items-center gap-3'>
        <div className='relative flex items-center'>
          <input
            ref={ref}
            id={id}
            type='checkbox'
            className='sr-only'
            checked={checked}
            disabled={disabled}
            onChange={handleChange}
            {...props}
          />
          <label htmlFor={id} className={baseStyles}>
            <div className={clsx(trackStyles, getTrackColor())}>
              <div
                className={thumbStyles}
                style={{
                  transform: getThumbTransform(),
                }}
              />
            </div>
          </label>
        </div>

        {label && (
          <Text
            el='label'
            variant='dim-2'
            htmlFor={id}
            className={clsx('cursor-pointer font-medium text-xs', {
              'text-dim-3': disabled,
              'text-error': error,
            })}>
            {label}
          </Text>
        )}
      </div>
    )
  },
)
